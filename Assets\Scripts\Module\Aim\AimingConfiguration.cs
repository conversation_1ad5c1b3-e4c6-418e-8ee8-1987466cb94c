using UnityEngine;
using Sirenix.OdinInspector;

namespace Module.Mono.Animancer.RealsticFemale
{
    /// <summary>
    /// Configuration for the aiming system, following the Scriptable Object pattern
    /// as specified in rules.md
    /// </summary>
    [CreateAssetMenu(fileName = "AimingConfiguration", menuName = "Configuration/AimingConfiguration")]
    public class AimingConfiguration : ScriptableObject
    {
        [Header("FOV Parameters")]
        [Toolt<PERSON>("Angle of the detection cone in degrees")]
        public float coneAngleBeforeAiming = 45f;
        public float coneAngleAfterAiming = 45f;

        [Tooltip("Range of the detection cone")]
        public float coneRange = 10f;

        [Tooltip("Radius of the detection sphere")]
        public float detectionRadius = 6f;

        [Header("Target Selection")]
        [Tooltip("Whether to allow targeting enemies outside the player's field of view")]
        public bool allowNonFOVTargets = false;

        [Tooltip("Base distance for normalization in target selection")]
        public float baseDistanceThreshold = 10f;

        [Tooltip("Base angle for normalization in target selection")]
        public float baseAngleThreshold = 45f;

        [Tooltip("Weight for distance in scoring")]
        [Range(0f, 1f)]
        public float distanceWeight = 0.7f;

        [Tooltip("Weight for angle in scoring")]
        [Range(0f, 1f)]
        public float angleWeight = 0.3f;

        [Tooltip("Prevents rapid switching between close targets")]
        [Range(0f, 1f)]
        public float targetSwitchHysteresis = 0.15f;

        [Header("Target Switching Parameters")]
        [Tooltip("Distance threshold for switching targets")] 
        public float TargetSwitchDistanceThreshold = 2.0f;
        [Tooltip("Minimum distance required to allow target switching")] 
        public float MinTargetSwitchDistance = 1.0f;
        [Tooltip("Angle threshold for switching targets (degrees)")]
        public float TargetSwitchAngleThreshold = 25.0f;
        [Tooltip("Score threshold for switching targets")]
        public float TargetSwitchScoreThreshold = 0.5f;

        [Header("AAA Aim Assist")]
        [Tooltip("Degrees to temporarily expand FOV when a target is near the edge")] public float dynamicFOVExpansion = 15f;
        [Tooltip("Angle in degrees for sticky targeting")] public float stickyAngle = 10f;
        [Tooltip("Distance in meters for sticky targeting")] public float stickyDistance = 2.5f;
        [Tooltip("Degrees per second for auto-rotation toward target")] public float aimAssistSpeed = 120f;

        [Tooltip("How quickly to interpolate to a new target")]
        [Range(0.01f, 1f)]
        public float targetInterpolationSpeed = 0.15f;

        [Header("Cooldown Settings")]
        [Tooltip("Time in seconds to wait before allowing aiming again after losing all enemies")]
        public float CooldownAfterLostAllEnenies = 1.0f;
        
        [Header("Debug")]
        [Tooltip("Show debug information about target selection")]
        public bool showTargetDebug = false;
    }
}
